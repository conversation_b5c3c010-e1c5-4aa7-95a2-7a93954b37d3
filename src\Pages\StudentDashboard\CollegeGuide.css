/* Custom styles for College Guide using Tailwind CSS */

/* Arabic font support */
.font-arabic {
  font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}

/* Text shadow for headers */
.text-shadow {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

/* Fade in animation for scroll button */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

/* Smooth scrolling for the entire page */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #3b82f6;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #2563eb;
}

/* Print styles */
@media print {
  .fixed {
    display: none !important;
  }
  
  .bg-gradient-to-br,
  .bg-gradient-to-r {
    background: white !important;
    color: black !important;
  }
  
  .shadow-lg,
  .shadow-xl,
  .shadow-2xl {
    box-shadow: none !important;
    border: 1px solid #e5e7eb !important;
  }
}

/* Responsive table styles */
@media (max-width: 768px) {
  .course-row {
    font-size: 0.875rem;
  }
}

/* Focus styles for accessibility */
button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Hover effects for interactive elements */
.hover-lift:hover {
  transform: translateY(-2px);
}

/* Custom border styles */
.border-r-4 {
  border-right-width: 4px;
}

/* RTL support improvements */
[dir="rtl"] .text-right {
  text-align: right;
}

[dir="rtl"] .text-left {
  text-align: left;
}

/* Custom spacing for Arabic text */
.leading-relaxed {
  line-height: 1.8;
}

/* Enhanced focus states */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}
