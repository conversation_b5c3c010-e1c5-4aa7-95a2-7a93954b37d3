/* College Guide Styles */
.college-guide-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  direction: rtl;
  text-align: right;
}

/* Header Section */
.header-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 40px;
  margin-bottom: 30px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 30px;
  color: white;
}

.logo-container {
  flex-shrink: 0;
}

.college-logo {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 4px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  object-fit: cover;
}

.title-section {
  flex: 1;
}

.main-title-ar {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 10px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  line-height: 1.2;
}

.main-title-en {
  font-size: 1.8rem;
  font-weight: 600;
  margin: 0 0 15px 0;
  opacity: 0.9;
  font-style: italic;
}

.subtitle-ar {
  font-size: 1.4rem;
  font-weight: 500;
  margin: 0 0 8px 0;
  opacity: 0.95;
}

.subtitle-en {
  font-size: 1.1rem;
  font-weight: 400;
  margin: 0 0 20px 0;
  opacity: 0.8;
}

.university-info {
  border-top: 2px solid rgba(255, 255, 255, 0.3);
  padding-top: 15px;
}

.university-ar {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 5px 0;
}

.university-en {
  font-size: 1rem;
  font-weight: 400;
  margin: 0;
  opacity: 0.9;
}

/* Decorative Line */
.decorative-line {
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
  border-radius: 2px;
  margin: 30px 0;
  box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

/* Content Sections */
.content-section {
  background: white;
  border-radius: 15px;
  padding: 30px;
  margin-bottom: 25px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(102, 126, 234, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.content-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
}

.section-title {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 25px 0;
  padding-bottom: 15px;
  border-bottom: 3px solid #667eea;
  position: relative;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -3px;
  right: 0;
  width: 60px;
  height: 3px;
  background: #764ba2;
  border-radius: 2px;
}

/* Table of Contents */
.toc-container {
  background: #f8f9fa;
  border-radius: 10px;
  padding: 20px;
  border-right: 4px solid #667eea;
}

.toc-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e9ecef;
  transition: background-color 0.3s ease;
}

.toc-item:last-child {
  border-bottom: none;
}

.toc-item:hover {
  background-color: rgba(102, 126, 234, 0.05);
  border-radius: 5px;
  padding-left: 10px;
  padding-right: 10px;
}

.toc-title {
  font-weight: 500;
  color: #2c3e50;
  flex: 1;
}

.toc-page {
  font-weight: 600;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
  min-width: 30px;
  text-align: center;
}

/* Text Content */
.text-content {
  line-height: 1.8;
}

.paragraph {
  font-size: 1.1rem;
  color: #34495e;
  margin: 0 0 20px 0;
  text-align: justify;
  line-height: 1.8;
}

/* Objectives List */
.objectives-list {
  margin-top: 20px;
}

.objective-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 10px;
  border-right: 4px solid #667eea;
  transition: all 0.3s ease;
}

.objective-item:hover {
  background: rgba(102, 126, 234, 0.05);
  transform: translateX(-5px);
}

.objective-number {
  font-weight: 700;
  color: #667eea;
  font-size: 1.2rem;
  margin-left: 15px;
  flex-shrink: 0;
}

.objective-text {
  color: #2c3e50;
  line-height: 1.7;
  text-align: justify;
}

/* Department Items */
.department-item {
  margin-bottom: 25px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border-right: 4px solid #28a745;
  transition: all 0.3s ease;
}

.department-item:hover {
  transform: translateX(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.department-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #155724;
  margin: 0 0 15px 0;
}

.department-description {
  color: #2c3e50;
  line-height: 1.7;
  text-align: justify;
  margin: 0;
}

/* Article Sections */
.article-section {
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
  border-radius: 12px;
  border-right: 4px solid #e53e3e;
}

.article-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: #c53030;
  margin: 0 0 15px 0;
}

/* Specializations List */
.specializations-list {
  list-style: none;
  padding: 0;
  margin: 15px 0 0 0;
}

.specializations-list li {
  padding: 10px 15px;
  margin: 8px 0;
  background: white;
  border-radius: 8px;
  border-right: 3px solid #667eea;
  font-weight: 500;
  color: #2c3e50;
  transition: all 0.3s ease;
}

.specializations-list li:hover {
  background: rgba(102, 126, 234, 0.05);
  transform: translateX(-5px);
}

/* Requirements Grid */
.requirements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.requirement-card {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
  border-radius: 12px;
  padding: 25px;
  border-right: 4px solid #4caf50;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.requirement-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.requirement-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2e7d32;
  margin: 0 0 15px 0;
  text-align: center;
}

.requirement-hours {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1b5e20;
  text-align: center;
  margin: 0 0 20px 0;
  padding: 10px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
}

.requirement-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.requirement-list li {
  padding: 8px 0;
  color: #2c3e50;
  font-weight: 500;
  border-bottom: 1px solid rgba(46, 125, 50, 0.2);
}

.requirement-list li:last-child {
  border-bottom: none;
}

/* Total Requirements */
.total-requirements {
  background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
  border-radius: 15px;
  padding: 30px;
  text-align: center;
  border: 2px solid #ff9800;
  box-shadow: 0 8px 25px rgba(255, 152, 0, 0.2);
}

.total-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #e65100;
  margin: 0 0 15px 0;
}

.total-hours {
  font-size: 3rem;
  font-weight: 900;
  color: #bf360c;
  margin: 0 0 15px 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.total-note {
  font-size: 1rem;
  color: #5d4037;
  margin: 0;
  font-style: italic;
  line-height: 1.6;
}

/* Footer Section */
.footer-section {
  background: linear-gradient(135deg, #263238 0%, #37474f 100%);
  border-radius: 15px;
  padding: 30px;
  margin-top: 40px;
  text-align: center;
  color: white;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.footer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
}

.footer-text {
  flex: 1;
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0;
  text-align: right;
}

.footer-logo {
  flex-shrink: 0;
}

.footer-logo-img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.3);
  object-fit: cover;
}

/* Print Styles */
@media print {
  .college-guide-container {
    background: white;
    box-shadow: none;
  }

  .header-section {
    background: #f5f5f5;
    color: black;
  }

  .content-section {
    box-shadow: none;
    border: 1px solid #ddd;
  }

  .footer-section {
    background: #f5f5f5;
    color: black;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .college-guide-container {
    padding: 15px;
  }

  .header-section {
    flex-direction: column;
    text-align: center;
    padding: 30px 20px;
  }

  .main-title-ar {
    font-size: 2rem;
  }

  .main-title-en {
    font-size: 1.5rem;
  }

  .content-section {
    padding: 20px;
  }

  .section-title {
    font-size: 1.6rem;
  }

  .objective-item {
    flex-direction: column;
  }

  .objective-number {
    margin-left: 0;
    margin-bottom: 10px;
  }

  .requirements-grid {
    grid-template-columns: 1fr;
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
  }

  .footer-text {
    text-align: center;
  }

  .total-hours {
    font-size: 2.5rem;
  }
}
