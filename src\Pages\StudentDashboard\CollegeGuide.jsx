import React from "react";
import "./CollegeGuide.css";
import collegeLogo from "../../assets/CollegeGuide.jpg";

const CollegeGuide = () => {
  return (
    <div className="college-guide-container">
      {/* Header Section */}
      <div className="header-section">
        <div className="logo-container">
          <img src={collegeLogo} alt="College Logo" className="college-logo" />
        </div>
        <div className="title-section">
          <h1 className="main-title-ar">اللائحة الداخلية لمرحلة البكالوريوس</h1>
          <h2 className="main-title-en">
            Internal Regulations for Bachelor's Degree
          </h2>
          <h3 className="subtitle-ar">بنظام الساعات المعتمدة</h3>
          <h4 className="subtitle-en">Credit Hours System</h4>
          <div className="university-info">
            <p className="university-ar">
              جامعة الزقازيق – كلية الحاسبات والمعلومات
            </p>
            <p className="university-en">
              Zagazig University – Faculty of Computers and Informatics
            </p>
          </div>
        </div>
      </div>

      {/* Decorative Line */}
      <div className="decorative-line"></div>

      {/* Table of Contents */}
      <div className="content-section">
        <h2 className="section-title">قائمة المحتويات</h2>
        <div className="toc-container">
          <div className="toc-item">
            <span className="toc-title">تمهيد</span>
            <span className="toc-page">4</span>
          </div>
          <div className="toc-item">
            <span className="toc-title">
              اللائحة الداخلية لمرحلة البكالوريوس بنظام الساعات المعتمدة
            </span>
            <span className="toc-page">5</span>
          </div>
          <div className="toc-item">
            <span className="toc-title">رؤية الكلية</span>
            <span className="toc-page">5</span>
          </div>
          <div className="toc-item">
            <span className="toc-title">رسالة الكلية</span>
            <span className="toc-page">5</span>
          </div>
          <div className="toc-item">
            <span className="toc-title">أهداف الكلية</span>
            <span className="toc-page">5</span>
          </div>
          <div className="toc-item">
            <span className="toc-title">الأقسام العلمية</span>
            <span className="toc-page">6</span>
          </div>
          <div className="toc-item">
            <span className="toc-title">
              المتطلبات الأكاديمية واللوائح المنظمة
            </span>
            <span className="toc-page">9</span>
          </div>
        </div>
      </div>

      {/* Introduction Section */}
      <div className="content-section">
        <h2 className="section-title">تمهيد</h2>
        <div className="text-content">
          <p className="paragraph">
            تم إنشاء كلية الحاسبات والمعلومات بجامعة الزقازيق بموجب قرار رئيس
            الجمهورية رقم (84) لسنة 1997م، حيث تم إصدار اللائحة الداخلية للكلية
            بموجب القرار الوزاري رقم (1209) بتاريخ 17/9/1998م. كما تم تعديل
            اللائحة الداخلية للكلية لمرحلتي (البكالوريوس والدراسات العليا) بموجب
            القرار الوزاري رقم (2645) بتاريخ 10/10/2006م. وأخيراً تم تعديل
            اللائحة الداخلية للكلية لمرحلة (الدراسات العليا) بموجب القرار
            الوزاري رقم (4290) بتاريخ 17/9/2018م.
          </p>
        </div>
      </div>

      {/* Vision Section */}
      <div className="content-section">
        <h2 className="section-title">رؤية الكلية</h2>
        <div className="text-content">
          <p className="paragraph">
            أن تكون كلية الحاسبات والمعلومات جامعة الزقازيق مؤسسة رائدة في
            التعليم العالي والبحث العلمي في مجالات الحوسبة والمعلوماتية ودعم
            القرار على المستوى المحلي والإقليمي والدولي.
          </p>
        </div>
      </div>

      {/* Mission Section */}
      <div className="content-section">
        <h2 className="section-title">رسالة الكلية</h2>
        <div className="text-content">
          <p className="paragraph">
            تلتزم كلية الحاسبات والمعلومات جامعة الزقازيق بتقديم خدمة تعليمية
            وبحثية متميزة لتخريج كوادر ذات قدرات تنافسية عالية من المتخصصين في
            مجالات الحوسبة والمعلوماتية ودعم القرار لديهم الدافعية للتعلم مدى
            الحياة والقدرة على مواجهة متطلبات العصر واحتياجات سوق العمل الحالية
            والمستقبلية وكذلك تقديم بحوث علمية تطبيقية تساهم في خدمة المجتمع.
          </p>
        </div>
      </div>

      {/* Objectives Section */}
      <div className="content-section">
        <h2 className="section-title">أهداف الكلية</h2>
        <div className="text-content">
          <p className="paragraph">
            تهدف كلية الحاسبات والمعلومات جامعة الزقازيق إلى:
          </p>
          <div className="objectives-list">
            <div className="objective-item">
              <span className="objective-number">1.</span>
              <span className="objective-text">
                إعداد المتخصصين في علوم الحاسبات والمعلومات والشبكات والوسائط
                المتعددة وبحوث العمليات ودعم القرار المؤهلين بالأسس النظرية
                ومناهج التطبيق بما يمكنهم من المنافسة العالمية في تطوير تقنيات
                الحاسبات والمعلومات.
              </span>
            </div>
            <div className="objective-item">
              <span className="objective-number">2.</span>
              <span className="objective-text">
                إجراء الدراسات والبحوث العلمية والتطبيقية في مجال الحاسبات
                والمعلومات التي لها أثر مباشر على التنمية المتكاملة في المجتمع.
              </span>
            </div>
            <div className="objective-item">
              <span className="objective-number">3.</span>
              <span className="objective-text">
                تقديم الاستشارات والمساعدات العلمية والفنية للهيئات والجهات التي
                تستخدم تقنيات الحاسبات والمعلومات وتهتم بصناعة ودعم اتخاذ
                القرار.
              </span>
            </div>
            <div className="objective-item">
              <span className="objective-number">4.</span>
              <span className="objective-text">
                إعادة تأهيل شباب الخريجين طبقاً لحاجة سوق العمل في المجالات
                المتعلقة بالحاسبات والمعلومات.
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Academic Departments Section */}
      <div className="content-section">
        <h2 className="section-title">الأقسام العلمية</h2>
        <div className="text-content">
          <p className="paragraph">تتكون الكلية من الأقسام العلمية التالية:</p>

          <div className="department-item">
            <h3 className="department-title">
              1. قسم علوم الحاسب (Computer Science)
            </h3>
            <p className="department-description">
              يدخل في اختصاصه تدريس وإجراء البحوث المتعلقة بالموضوعات والتخصصات
              العلمية التالية: أساسيات ومفاهيم علوم الحاسب، أساسيات لغات
              البرمجة، البرمجة الهيكلية، البرمجة الكائنية، البرمجة المنطقية،
              نظرية تصميم المترجمات، تحليل وتصميم الخوارزميات، اللغات الصورية
              ونظرية الآليات، نظم التشغيل، بناء وتنظيم الحاسبات، هياكل البيانات
              وتنظيم الملفات.
            </p>
          </div>

          <div className="department-item">
            <h3 className="department-title">
              2. قسم نظم المعلومات (Information Systems)
            </h3>
            <p className="department-description">
              يدخل في اختصاصه تدريس وإجراء البحوث المتعلقة بالموضوعات والتخصصات
              العلمية التالية: نظم المعلومات، تحليل وتصميم النظم، تخزين واسترجاع
              المعلومات، نظم قواعد البيانات، استخلاص البيانات، قواعد البيانات
              الموزعة، نظم المعلومات الذكية، نظم معلومات الوسائط المتعددة.
            </p>
          </div>

          <div className="department-item">
            <h3 className="department-title">
              3. قسم تكنولوجيا المعلومات (Information Technology)
            </h3>
            <p className="department-description">
              يدخل في اختصاصه تدريس وإجراء البحوث المتعلقة بالموضوعات والتخصصات
              العلمية التالية: شبكات الحاسب وإدارتها، أمن الشبكات، برمجة
              الشبكات، تراسل البيانات، تكنولوجيا الاتصالات، معالجة الإشارات
              الرقمية والعشوائية، التعرف على الكلام وتوليده، معالجة الصور.
            </p>
          </div>

          <div className="department-item">
            <h3 className="department-title">
              4. قسم بحوث العمليات ودعم القرار (Operations Research and Decision
              Support)
            </h3>
            <p className="department-description">
              يدخل في اختصاصه تدريس وإجراء البحوث المتعلقة بالمقررات والتخصصات
              التالية: أساسيات ومفاهيم ونظريات اتخاذ القرار، بحوث العمليات
              ومنهجية دعم القرار، البرمجة الخطية وغير الخطية، البرمجة العشوائية
              والديناميكية، نظرية الشبكات وتخطيط المشروعات.
            </p>
          </div>
        </div>
      </div>

      {/* Academic Requirements Section */}
      <div className="content-section">
        <h2 className="section-title">المتطلبات الأكاديمية واللوائح المنظمة</h2>

        <div className="article-section">
          <h3 className="article-title">مادة (1): الدرجة العلمية</h3>
          <p className="paragraph">
            تمنح جامعة الزقازيق بناء على طلب مجلس الكلية درجة بكالوريوس الحاسبات
            والمعلومات في إحدى التخصصات التالية:
          </p>
          <ul className="specializations-list">
            <li>1- علوم الحاسب (Computer Science)</li>
            <li>2- نظم المعلومات (Information Systems)</li>
            <li>3- الجيوإنفورماتيك (Geoinformatics)</li>
            <li>4- تكنولوجيا المعلومات (Information Technology)</li>
            <li>
              5- بحوث العمليات ودعم القرار (Operations Research and Decision
              Support)
            </li>
          </ul>
        </div>

        <div className="article-section">
          <h3 className="article-title">مادة (2): قواعد القبول</h3>
          <p className="paragraph">
            يتم قبول الطالب للدراسة بالكلية بناء على القواعد التي يحددها مكتب
            تنسيق القبول بالجامعات كل عام من بين الطلاب الحاصلين على الثانوية
            العامة أو ما يعادلها على أن يكون قد درس مقرر الفيزياء ومقرر رياضة
            (2).
          </p>
        </div>

        <div className="article-section">
          <h3 className="article-title">مادة (3): نظام الدراسة</h3>
          <p className="paragraph">
            أ- تعتمد الدراسة بالكلية على نظام الساعات المعتمدة، ويقسم العام
            الدراسي إلى فصلين دراسيين نظاميين، وتكون الساعة المعتمدة هي وحدة
            قياس دراسية لتحديد وزن المقرر الدراسي.
          </p>
          <p className="paragraph">
            ب- يجوز لمجلس الكلية الموافقة على عقد فصول صيفية مكثفة في بعض
            المقررات بناء على اقتراح لجنة شئون التعليم والطلاب وفقاً لما تسمح به
            إمكانيات وظروف الكلية.
          </p>
          <p className="paragraph">
            ج- الدراسة في المستوى الأول والثاني عامة لجميع تخصصات الكلية ويبدأ
            التخصص في المستوى الثالث عند اجتياز الطالب أكثر من 60 ساعة. ولكل قسم
            أن يضع الشروط المؤهلة للالتحاق به بعد إقرارها من مجلس الكلية.
          </p>
          <p className="paragraph">
            د- يتطلب الحصول على البكالوريوس أن يجتاز الطالب بنجاح 138 ساعة
            معتمدة وذلك على مدى ثمانية فصول دراسية على الأقل، مقسمة إلى أربعة
            مستويات دراسية.
          </p>
        </div>

        <div className="article-section">
          <h3 className="article-title">مادة (4): لغة التدريس</h3>
          <p className="paragraph">
            الدراسة في الكلية باللغة الإنجليزية عدا مقرر حقوق الإنسان ومكافحة
            الفساد (باللغة العربية).
          </p>
        </div>

        <div className="article-section">
          <h3 className="article-title">مادة (5): الإرشاد الأكاديمي</h3>
          <p className="paragraph">
            تحدد الكلية لكل مجموعة من الطلاب مرشداً أكاديمياً من أعضاء هيئة
            التدريس، يقوم بمهام الإرشاد الأكاديمي للطالب ومساعدته على اختيار
            المقررات التي يدرسها والتسجيل فيها وتوجيهه طوال فترة دراسته بالكلية،
            ويعتبر رأي المرشد الأكاديمي استشارياً والطالب هو المسئول عن المقررات
            التي يقوم بالتسجيل فيها بناء على رغبته بشرط أن يكون الطالب قد اجتاز
            بنجاح متطلب التسجيل لهذا المقرر.
          </p>
        </div>
      </div>

      {/* Graduation Requirements Section */}
      <div className="content-section">
        <h2 className="section-title">متطلبات التخرج</h2>

        <div className="requirements-grid">
          <div className="requirement-card">
            <h3 className="requirement-title">
              المتطلبات العامة (متطلبات الجامعة)
            </h3>
            <p className="requirement-hours">12 ساعة معتمدة</p>
            <ul className="requirement-list">
              <li>المقررات الإجبارية: 6 ساعات معتمدة إجبارية</li>
              <li>المقررات الاختيارية: 6 ساعات معتمدة</li>
            </ul>
          </div>

          <div className="requirement-card">
            <h3 className="requirement-title">متطلبات الكلية</h3>
            <p className="requirement-hours">66 ساعة معتمدة</p>
            <ul className="requirement-list">
              <li>علوم أساسية: 24 ساعة معتمدة إجبارية</li>
              <li>علوم حاسب أساسية: 42 ساعة معتمدة إجبارية</li>
            </ul>
          </div>

          <div className="requirement-card">
            <h3 className="requirement-title">متطلبات التخصص</h3>
            <p className="requirement-hours">60 ساعة معتمدة</p>
            <ul className="requirement-list">
              <li>المقررات الإجبارية: 42 ساعة معتمدة إجبارية</li>
              <li>المقررات الاختيارية: 12 ساعة معتمدة</li>
              <li>مشروع التخرج: 6 ساعات معتمدة</li>
            </ul>
          </div>
        </div>

        <div className="total-requirements">
          <h3 className="total-title">إجمالي الساعات المطلوبة للتخرج</h3>
          <div className="total-hours">138 ساعة معتمدة</div>
          <p className="total-note">
            بالإضافة إلى التدريب العملي والميداني ومقرر حقوق الإنسان ومكافحة
            الفساد (لا يحسب في المعدل التراكمي للطالب)
          </p>
        </div>
      </div>

      {/* Footer Section */}
      <div className="footer-section">
        <div className="footer-content">
          <p className="footer-text">
            هذه اللائحة معتمدة من مجلس الكلية ومجلس الجامعة وفقاً للقوانين
            واللوائح المنظمة للتعليم العالي في جمهورية مصر العربية
          </p>
          <div className="footer-logo">
            <img
              src={collegeLogo}
              alt="College Logo"
              className="footer-logo-img"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default CollegeGuide;
