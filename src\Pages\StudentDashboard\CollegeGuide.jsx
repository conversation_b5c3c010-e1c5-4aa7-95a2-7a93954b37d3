import React, { useState, useEffect } from "react";
import { HiChevronDown, HiArrowUp } from "react-icons/hi2";

import collegeLogo from "../../assets/CollegeGuide.jpg";
import "./CollegeGuide.css";

const CollegeGuide = () => {
  const [showScrollButton, setShowScrollButton] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setShowScrollButton(window.pageYOffset > 300);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const scrollToBottom = () => {
    window.scrollTo({
      top: document.documentElement.scrollHeight,
      behavior: "smooth",
    });
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    <div
      className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 p-4 md:p-6 lg:p-8 font-arabic relative"
      style={{ direction: "rtl" }}
    >
      {/* Header Section */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-2xl p-6 md:p-8 lg:p-10 mb-8 shadow-2xl">
        <div className="flex flex-col lg:flex-row items-center gap-6 lg:gap-8 text-white">
          <div className="flex-shrink-0">
            <img
              src={collegeLogo}
              alt="College Logo"
              className="w-24 h-24 md:w-32 md:h-32 lg:w-40 lg:h-40 rounded-full border-4 border-white/30 shadow-lg object-cover"
            />
          </div>
          <div className="flex-1 text-center lg:text-right">
            <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-2 lg:mb-3">
              اللائحة الداخلية لمرحلة البكالوريوس
            </h1>
            <h2 className="text-lg md:text-xl lg:text-2xl font-semibold mb-3 lg:mb-4 opacity-90 italic">
              Internal Regulations for Bachelor's Degree
            </h2>
            <h3 className="text-base md:text-lg lg:text-xl font-medium mb-2 lg:mb-3 opacity-95">
              بنظام الساعات المعتمدة
            </h3>
            <h4 className="text-sm md:text-base lg:text-lg font-normal mb-4 lg:mb-5 opacity-80">
              Credit Hours System
            </h4>
            <div className="border-t border-white/30 pt-4">
              <p className="text-base md:text-lg lg:text-xl font-semibold mb-1">
                جامعة الزقازيق – كلية الحاسبات والمعلومات
              </p>
              <p className="text-sm md:text-base lg:text-lg opacity-90">
                Zagazig University – Faculty of Computers and Informatics
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Decorative Line */}
      <div className="h-1 bg-gradient-to-r from-blue-500 via-blue-600 to-blue-500 rounded-full mb-8 shadow-sm"></div>

      {/* Table of Contents */}
      <div className="bg-white rounded-xl shadow-lg p-6 md:p-8 mb-8 border border-gray-200">
        <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-6 pb-3 border-b-2 border-blue-500 relative">
          قائمة المحتويات
          <span className="absolute bottom-0 right-0 w-16 h-0.5 bg-blue-700"></span>
        </h2>
        <div className="bg-gray-50 rounded-lg p-4 md:p-6 border-r-4 border-blue-500">
          <div className="space-y-3">
            <div className="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 hover:bg-blue-50">
              <span className="text-gray-700 font-medium">تمهيد</span>
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-semibold">
                4
              </span>
            </div>
            <div className="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 hover:bg-blue-50">
              <span className="text-gray-700 font-medium">
                اللائحة الداخلية لمرحلة البكالوريوس بنظام الساعات المعتمدة
              </span>
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-semibold">
                5
              </span>
            </div>
            <div className="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 hover:bg-blue-50">
              <span className="text-gray-700 font-medium">رؤية الكلية</span>
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-semibold">
                5
              </span>
            </div>
            <div className="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 hover:bg-blue-50">
              <span className="text-gray-700 font-medium">رسالة الكلية</span>
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-semibold">
                5
              </span>
            </div>
            <div className="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 hover:bg-blue-50">
              <span className="text-gray-700 font-medium">أهداف الكلية</span>
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-semibold">
                5
              </span>
            </div>
            <div className="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 hover:bg-blue-50">
              <span className="text-gray-700 font-medium">الأقسام العلمية</span>
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-semibold">
                6
              </span>
            </div>
            <div className="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 hover:bg-blue-50">
              <span className="text-gray-700 font-medium">
                المتطلبات الأكاديمية واللوائح المنظمة
              </span>
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-semibold">
                9
              </span>
            </div>
            <div className="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 hover:bg-blue-50">
              <span className="text-gray-700 font-medium">متطلبات التخرج</span>
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-semibold">
                15
              </span>
            </div>
            <div className="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 hover:bg-blue-50">
              <span className="text-gray-700 font-medium">
                الجداول الدراسية
              </span>
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-semibold">
                18
              </span>
            </div>
            <div className="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 hover:bg-blue-50">
              <span className="text-gray-700 font-medium">
                التدريب العملي ومشروع التخرج
              </span>
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-semibold">
                25
              </span>
            </div>
            <div className="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 hover:bg-blue-50">
              <span className="text-gray-700 font-medium">
                مواد إضافية ولوائح تنظيمية
              </span>
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-semibold">
                28
              </span>
            </div>
            <div className="flex justify-between items-center p-3 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 hover:bg-blue-50">
              <span className="text-gray-700 font-medium">
                نظام التقويم والامتحانات
              </span>
              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-semibold">
                32
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Introduction Section */}
      <div className="bg-white rounded-xl shadow-lg p-6 md:p-8 mb-8 border border-gray-200 hover:shadow-xl transition-shadow duration-300">
        <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-6 pb-3 border-b-2 border-blue-500 relative">
          تمهيد
          <span className="absolute bottom-0 right-0 w-16 h-0.5 bg-blue-700"></span>
        </h2>
        <div className="prose prose-lg max-w-none">
          <p className="text-gray-700 leading-relaxed text-justify mb-4 text-base md:text-lg">
            تم إنشاء كلية الحاسبات والمعلومات بجامعة الزقازيق بموجب قرار رئيس
            الجمهورية رقم (84) لسنة 1997م، حيث تم إصدار اللائحة الداخلية للكلية
            بموجب القرار الوزاري رقم (1209) بتاريخ 17/9/1998م. كما تم تعديل
            اللائحة الداخلية للكلية لمرحلتي (البكالوريوس والدراسات العليا) بموجب
            القرار الوزاري رقم (2645) بتاريخ 10/10/2006م. وأخيراً تم تعديل
            اللائحة الداخلية للكلية لمرحلة (الدراسات العليا) بموجب القرار
            الوزاري رقم (4290) بتاريخ 17/9/2018م.
          </p>
        </div>
      </div>

      {/* Vision Section */}
      <div className="bg-white rounded-xl shadow-lg p-6 md:p-8 mb-8 border border-gray-200 hover:shadow-xl transition-shadow duration-300">
        <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-6 pb-3 border-b-2 border-blue-500 relative">
          رؤية الكلية
          <span className="absolute bottom-0 right-0 w-16 h-0.5 bg-blue-700"></span>
        </h2>
        <div className="bg-blue-50 rounded-lg p-6 border-r-4 border-blue-500">
          <p className="text-gray-700 leading-relaxed text-justify text-base md:text-lg">
            أن تكون كلية الحاسبات والمعلومات جامعة الزقازيق مؤسسة رائدة في
            التعليم العالي والبحث العلمي في مجالات الحوسبة والمعلوماتية ودعم
            القرار على المستوى المحلي والإقليمي والدولي.
          </p>
        </div>
      </div>

      {/* Mission Section */}
      <div className="bg-white rounded-xl shadow-lg p-6 md:p-8 mb-8 border border-gray-200 hover:shadow-xl transition-shadow duration-300">
        <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-6 pb-3 border-b-2 border-blue-500 relative">
          رسالة الكلية
          <span className="absolute bottom-0 right-0 w-16 h-0.5 bg-blue-700"></span>
        </h2>
        <div className="bg-gray-50 rounded-lg p-6 border-r-4 border-gray-500">
          <p className="text-gray-700 leading-relaxed text-justify text-base md:text-lg">
            تلتزم كلية الحاسبات والمعلومات جامعة الزقازيق بتقديم خدمة تعليمية
            وبحثية متميزة لتخريج كوادر ذات قدرات تنافسية عالية من المتخصصين في
            مجالات الحوسبة والمعلوماتية ودعم القرار لديهم الدافعية للتعلم مدى
            الحياة والقدرة على مواجهة متطلبات العصر واحتياجات سوق العمل الحالية
            والمستقبلية وكذلك تقديم بحوث علمية تطبيقية تساهم في خدمة المجتمع.
          </p>
        </div>
      </div>

      {/* Objectives Section */}
      <div className="bg-white rounded-xl shadow-lg p-6 md:p-8 mb-8 border border-gray-200 hover:shadow-xl transition-shadow duration-300">
        <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-6 pb-3 border-b-2 border-blue-500 relative">
          أهداف الكلية
          <span className="absolute bottom-0 right-0 w-16 h-0.5 bg-blue-700"></span>
        </h2>
        <div className="prose prose-lg max-w-none">
          <p className="text-gray-700 text-base md:text-lg mb-6">
            تهدف كلية الحاسبات والمعلومات جامعة الزقازيق إلى:
          </p>
          <div className="space-y-4">
            <div className="flex items-start gap-4 p-4 bg-gray-50 rounded-lg border-r-4 border-blue-500 hover:bg-blue-50 transition-colors duration-300">
              <span className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-sm">
                1
              </span>
              <span className="text-gray-700 leading-relaxed text-justify">
                إعداد المتخصصين في علوم الحاسبات والمعلومات والشبكات والوسائط
                المتعددة وبحوث العمليات ودعم القرار المؤهلين بالأسس النظرية
                ومناهج التطبيق بما يمكنهم من المنافسة العالمية في تطوير تقنيات
                الحاسبات والمعلومات.
              </span>
            </div>
            <div className="flex items-start gap-4 p-4 bg-gray-50 rounded-lg border-r-4 border-blue-500 hover:bg-blue-50 transition-colors duration-300">
              <span className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-sm">
                2
              </span>
              <span className="text-gray-700 leading-relaxed text-justify">
                إجراء الدراسات والبحوث العلمية والتطبيقية في مجال الحاسبات
                والمعلومات التي لها أثر مباشر على التنمية المتكاملة في المجتمع.
              </span>
            </div>
            <div className="flex items-start gap-4 p-4 bg-gray-50 rounded-lg border-r-4 border-blue-500 hover:bg-blue-50 transition-colors duration-300">
              <span className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-sm">
                3
              </span>
              <span className="text-gray-700 leading-relaxed text-justify">
                تقديم الاستشارات والمساعدات العلمية والفنية للهيئات والجهات التي
                تستخدم تقنيات الحاسبات والمعلومات وتهتم بصناعة ودعم اتخاذ
                القرار.
              </span>
            </div>
            <div className="flex items-start gap-4 p-4 bg-gray-50 rounded-lg border-r-4 border-blue-500 hover:bg-blue-50 transition-colors duration-300">
              <span className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-sm">
                4
              </span>
              <span className="text-gray-700 leading-relaxed text-justify">
                إعادة تأهيل شباب الخريجين طبقاً لحاجة سوق العمل في المجالات
                المتعلقة بالحاسبات والمعلومات.
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Academic Departments Section */}
      <div className="content-section">
        <h2 className="section-title">الأقسام العلمية</h2>
        <div className="text-content">
          <p className="paragraph">تتكون الكلية من الأقسام العلمية التالية:</p>

          <div className="department-item">
            <h3 className="department-title">
              1. قسم علوم الحاسب (Computer Science)
            </h3>
            <p className="department-description">
              يدخل في اختصاصه تدريس وإجراء البحوث المتعلقة بالموضوعات والتخصصات
              العلمية التالية: أساسيات ومفاهيم علوم الحاسب، أساسيات لغات
              البرمجة، البرمجة الهيكلية، البرمجة الكائنية، البرمجة المنطقية،
              نظرية تصميم المترجمات، تحليل وتصميم الخوارزميات، اللغات الصورية
              ونظرية الآليات، نظم التشغيل، بناء وتنظيم الحاسبات، هياكل البيانات
              وتنظيم الملفات.
            </p>
          </div>

          <div className="department-item">
            <h3 className="department-title">
              2. قسم نظم المعلومات (Information Systems)
            </h3>
            <p className="department-description">
              يدخل في اختصاصه تدريس وإجراء البحوث المتعلقة بالموضوعات والتخصصات
              العلمية التالية: نظم المعلومات، تحليل وتصميم النظم، تخزين واسترجاع
              المعلومات، نظم قواعد البيانات، استخلاص البيانات، قواعد البيانات
              الموزعة، نظم المعلومات الذكية، نظم معلومات الوسائط المتعددة.
            </p>
          </div>

          <div className="department-item">
            <h3 className="department-title">
              3. قسم تكنولوجيا المعلومات (Information Technology)
            </h3>
            <p className="department-description">
              يدخل في اختصاصه تدريس وإجراء البحوث المتعلقة بالموضوعات والتخصصات
              العلمية التالية: شبكات الحاسب وإدارتها، أمن الشبكات، برمجة
              الشبكات، تراسل البيانات، تكنولوجيا الاتصالات، معالجة الإشارات
              الرقمية والعشوائية، التعرف على الكلام وتوليده، معالجة الصور.
            </p>
          </div>

          <div className="department-item">
            <h3 className="department-title">
              4. قسم بحوث العمليات ودعم القرار (Operations Research and Decision
              Support)
            </h3>
            <p className="department-description">
              يدخل في اختصاصه تدريس وإجراء البحوث المتعلقة بالمقررات والتخصصات
              التالية: أساسيات ومفاهيم ونظريات اتخاذ القرار، بحوث العمليات
              ومنهجية دعم القرار، البرمجة الخطية وغير الخطية، البرمجة العشوائية
              والديناميكية، نظرية الشبكات وتخطيط المشروعات.
            </p>
          </div>
        </div>
      </div>

      {/* Academic Requirements Section */}
      <div className="content-section">
        <h2 className="section-title">المتطلبات الأكاديمية واللوائح المنظمة</h2>

        <div className="article-section">
          <h3 className="article-title">مادة (1): الدرجة العلمية</h3>
          <p className="paragraph">
            تمنح جامعة الزقازيق بناء على طلب مجلس الكلية درجة بكالوريوس الحاسبات
            والمعلومات في إحدى التخصصات التالية:
          </p>
          <ul className="specializations-list">
            <li>1- علوم الحاسب (Computer Science)</li>
            <li>2- نظم المعلومات (Information Systems)</li>
            <li>3- الجيوإنفورماتيك (Geoinformatics)</li>
            <li>4- تكنولوجيا المعلومات (Information Technology)</li>
            <li>
              5- بحوث العمليات ودعم القرار (Operations Research and Decision
              Support)
            </li>
          </ul>
        </div>

        <div className="article-section">
          <h3 className="article-title">مادة (2): قواعد القبول</h3>
          <p className="paragraph">
            يتم قبول الطالب للدراسة بالكلية بناء على القواعد التي يحددها مكتب
            تنسيق القبول بالجامعات كل عام من بين الطلاب الحاصلين على الثانوية
            العامة أو ما يعادلها على أن يكون قد درس مقرر الفيزياء ومقرر رياضة
            (2).
          </p>
        </div>

        <div className="article-section">
          <h3 className="article-title">مادة (3): نظام الدراسة</h3>
          <p className="paragraph">
            أ- تعتمد الدراسة بالكلية على نظام الساعات المعتمدة، ويقسم العام
            الدراسي إلى فصلين دراسيين نظاميين، وتكون الساعة المعتمدة هي وحدة
            قياس دراسية لتحديد وزن المقرر الدراسي.
          </p>
          <p className="paragraph">
            ب- يجوز لمجلس الكلية الموافقة على عقد فصول صيفية مكثفة في بعض
            المقررات بناء على اقتراح لجنة شئون التعليم والطلاب وفقاً لما تسمح به
            إمكانيات وظروف الكلية.
          </p>
          <p className="paragraph">
            ج- الدراسة في المستوى الأول والثاني عامة لجميع تخصصات الكلية ويبدأ
            التخصص في المستوى الثالث عند اجتياز الطالب أكثر من 60 ساعة. ولكل قسم
            أن يضع الشروط المؤهلة للالتحاق به بعد إقرارها من مجلس الكلية.
          </p>
          <p className="paragraph">
            د- يتطلب الحصول على البكالوريوس أن يجتاز الطالب بنجاح 138 ساعة
            معتمدة وذلك على مدى ثمانية فصول دراسية على الأقل، مقسمة إلى أربعة
            مستويات دراسية.
          </p>
        </div>

        <div className="article-section">
          <h3 className="article-title">مادة (4): لغة التدريس</h3>
          <p className="paragraph">
            الدراسة في الكلية باللغة الإنجليزية عدا مقرر حقوق الإنسان ومكافحة
            الفساد (باللغة العربية).
          </p>
        </div>

        <div className="article-section">
          <h3 className="article-title">مادة (5): الإرشاد الأكاديمي</h3>
          <p className="paragraph">
            تحدد الكلية لكل مجموعة من الطلاب مرشداً أكاديمياً من أعضاء هيئة
            التدريس، يقوم بمهام الإرشاد الأكاديمي للطالب ومساعدته على اختيار
            المقررات التي يدرسها والتسجيل فيها وتوجيهه طوال فترة دراسته بالكلية،
            ويعتبر رأي المرشد الأكاديمي استشارياً والطالب هو المسئول عن المقررات
            التي يقوم بالتسجيل فيها بناء على رغبته بشرط أن يكون الطالب قد اجتاز
            بنجاح متطلب التسجيل لهذا المقرر.
          </p>
        </div>

        <div className="article-section">
          <h3 className="article-title">مادة (6): التسجيل والحذف والإضافة</h3>
          <p className="paragraph">
            أ- مع بداية كل فصل دراسي يقوم الطالب بتسجيل المقررات الدراسية التي
            يختارها، وذلك في الأوقات التي تحددها إدارة الكلية قبل بدء انتظام
            الدراسة.
          </p>
          <p className="paragraph">
            ب- يحدد مجلس الكلية الحد الأدنى لعدد الطلاب للتسجيل في كل مقرر بناء
            على اقتراح لجنة شئون التعليم والطلاب.
          </p>
          <div className="sub-section">
            <h4 className="sub-title">عدد ساعات التسجيل:</h4>
            <div className="registration-rules">
              <div className="rule-item">
                <h5>بالنسبة للفصول النظامية:</h5>
                <ul>
                  <li>الحد الأدنى للساعات المعتمدة للتسجيل: 9 ساعات</li>
                  <li>الحد الأقصى للساعات المسجلة للطالب: 18 ساعة معتمدة</li>
                  <li>الحد الأقصى للطلاب المراقبين علمياً: 12 ساعة معتمدة</li>
                  <li>
                    يمكن زيادة الحد الأقصى للطلاب الحاصلين على معدل تراكمي 2.0
                    أو أعلى إلى 21 ساعة معتمدة
                  </li>
                </ul>
              </div>
              <div className="rule-item">
                <h5>بالنسبة للفصل الصيفي:</h5>
                <ul>
                  <li>الحد الأقصى للساعات المسجلة للطالب: 6 ساعات معتمدة</li>
                  <li>
                    يمكن زيادة الحد الأقصى إلى 9 ساعات معتمدة لدواعي تخرج الطالب
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div className="article-section">
          <h3 className="article-title">مادة (7): الانسحاب من المقرر</h3>
          <p className="paragraph">
            أ- يجوز للطالب بعد تسجيل المقررات التي اختارها أن ينسحب من مقرر أو
            أكثر خلال فترة محددة يعلن عنها مجلس الكلية بحيث لا يقل عدد الساعات
            المسجلة للطالب عن الحد الأدنى للتسجيل في الفصل الدراسي الواحد وفي
            هذه الحالة لا يعد الطالب راسباً في المقررات التي انسحب منها ويحسب له
            تقدير "منسحب" في سجله الأكاديمي.
          </p>
          <p className="paragraph">
            ب- إذا انسحب الطالب من مقرر أو أكثر بعد الفترة المحددة لذلك دون عذر
            قهري يقبله مجلس الكلية يحتسب له تقدير "راسب" في المقررات التي انسحب
            منها.
          </p>
        </div>

        <div className="article-section">
          <h3 className="article-title">مادة (8): المواظبة والغياب</h3>
          <p className="paragraph">
            أ- الدراسة في الكلية نظامية وتخضع لعملية متابعة حضور الطالب لشروط
            ولوائح تحددها إدارة الكلية.
          </p>
          <p className="paragraph">
            ب- يتطلب دخول الطالب الامتحان النهائي تحقيق نسبة حضور لا تقل عن 75%
            من المحاضرات والتمارين داخل الحرم الجامعي في كل مقرر، وإذا تجاوزت
            نسبة غياب الطالب – دون عذر مقبول – في أحد المقررات 25% يكون لمجلس
            الكلية حرمانه من دخول الامتحان النهائي بعد إنذاره وفقاً للقواعد
            المنظمة لذلك، ويعتبر راسباً ويسجل حرمان.
          </p>
          <p className="paragraph">
            ج- الطالب الذي يغيب عن الامتحان النهائي لأي مقرر – دون عذر مقبول –
            يعتبر راسباً ويسجل غياب في ذلك المقرر، ويتعين عليه إعادة دراسة
            المقرر مرة أخرى.
          </p>
        </div>

        <div className="article-section">
          <h3 className="article-title">مادة (9): الانقطاع عن الدراسة</h3>
          <p className="paragraph">
            أ- يعتبر الطالب منقطعاً عن الدراسة إذا غاب عن الحضور في جميع مقررات
            الفصل الدراسي دونب عذر مقبول أو لم يسجل المقررات في فصل دراسي خلال
            مواعيد التسجيل المقررة.
          </p>
          <p className="paragraph">
            ب- يجوز للطالب أن يتقدم بطلب إيقاف القيد بالكلية حسب الشروط والضوابط
            التي تضعها الجامعة.
          </p>
        </div>

        <div className="article-section">
          <h3 className="article-title">مادة (10): الفصل من الكلية</h3>
          <p className="paragraph">
            أ- إذا انخفض المعدل التراكمي للطالب إلى أقل من 2 في أي فصل دراسي
            رئيسي يوجه له إنذار أكاديمي، يقضي بضرورة رفع الطالب لمعدله التراكمي
            إلى 2 على الأقل.
          </p>
          <p className="paragraph">
            ب- يفصل الطالب المنذر أكاديمياً من الدراسة بالكلية في الحالات
            التالية:
          </p>
          <ul className="dismissal-list">
            <li>
              إذا تكرر انخفاض معدله التراكمي عن 2 في أربعة فصول دراسية رئيسية
              متتابعة
            </li>
            <li>
              إذا انقطع عن الدراسة لمدة أطول من فصلين دراسيين نظاميين متتاليين
              أو ثلاث فصول دراسية نظامية غير متتالية دون عذر يقبله مجلس الكلية
            </li>
          </ul>
        </div>

        <div className="article-section">
          <h3 className="article-title">مادة (11): نظام الامتحانات</h3>
          <p className="paragraph">أ- الدرجة العظمى لكل مقرر هي 100 درجة.</p>
          <p className="paragraph">
            ب- الحد الأدنى للنجاح في المقرر الدراسي هو 50% من مجموع درجات
            المقرر، و 30% على الأقل من درجات الامتحان التحريري.
          </p>
          <div className="grades-distribution">
            <h4 className="sub-title">
              توزيع درجات الامتحان في كل مقرر على النحو التالي:
            </h4>
            <div className="grade-item">
              <span className="grade-percentage">60%</span>
              <span className="grade-description">
                للامتحان التحريري نهاية الفصل الدراسي
              </span>
            </div>
            <div className="grade-item">
              <span className="grade-percentage">40%</span>
              <span className="grade-description">
                يتم توزيعها لتشمل الأعمال الفصلية على النحو التالي:
              </span>
            </div>
            <div className="sub-grades">
              <div className="sub-grade-item">
                <span className="sub-percentage">15%</span>
                <span>
                  للامتحانات التي يجريها الأستاذ بصفة دورية والامتحانات العملية
                </span>
              </div>
              <div className="sub-grade-item">
                <span className="sub-percentage">15%</span>
                <span>امتحان منتصف الفصل الدراسي</span>
              </div>
              <div className="sub-grade-item">
                <span className="sub-percentage">10%</span>
                <span>امتحانات شفوية</span>
              </div>
            </div>
          </div>
        </div>

        <div className="article-section">
          <h3 className="article-title">مادة (12): نظام التقويم</h3>
          <p className="paragraph">
            يكون نظام التقييم على أساس التقدير في كل مقرر دراسي بنظام النقاط
            الذي يحدد طبقاً للجدول التالي:
          </p>
          <div className="grading-table">
            <div className="table-header">
              <span>النسبة المئوية (%)</span>
              <span>الرمز</span>
              <span>النقاط</span>
              <span>التقدير</span>
            </div>
            <div className="table-row">
              <span>من 90% إلى 100%</span>
              <span>A+</span>
              <span>4.0</span>
              <span>ممتاز</span>
            </div>
            <div className="table-row">
              <span>من 85% إلى أقل من 90%</span>
              <span>A</span>
              <span>3.7</span>
              <span>ممتاز</span>
            </div>
            <div className="table-row">
              <span>من 80% إلى أقل من 85%</span>
              <span>B+</span>
              <span>3.3</span>
              <span>جيد جداً</span>
            </div>
            <div className="table-row">
              <span>من 75% إلى أقل من 80%</span>
              <span>B</span>
              <span>3.0</span>
              <span>جيد جداً</span>
            </div>
            <div className="table-row">
              <span>من 70% إلى أقل من 75%</span>
              <span>C+</span>
              <span>2.7</span>
              <span>جيد</span>
            </div>
            <div className="table-row">
              <span>من 65% إلى أقل من 70%</span>
              <span>C</span>
              <span>2.4</span>
              <span>جيد</span>
            </div>
            <div className="table-row">
              <span>من 60% إلى أقل من 65%</span>
              <span>D+</span>
              <span>2.2</span>
              <span>مقبول</span>
            </div>
            <div className="table-row">
              <span>من 50% إلى أقل من 60%</span>
              <span>D</span>
              <span>2.0</span>
              <span>مقبول</span>
            </div>
            <div className="table-row fail">
              <span>أقل من 50%</span>
              <span>F</span>
              <span>صفر</span>
              <span>راسب</span>
            </div>
          </div>
        </div>
      </div>

      {/* Graduation Requirements Section */}
      <div className="content-section">
        <h2 className="section-title">متطلبات التخرج</h2>

        <div className="requirements-grid">
          <div className="requirement-card">
            <h3 className="requirement-title">
              المتطلبات العامة (متطلبات الجامعة)
            </h3>
            <p className="requirement-hours">12 ساعة معتمدة</p>
            <ul className="requirement-list">
              <li>المقررات الإجبارية: 6 ساعات معتمدة إجبارية</li>
              <li>المقررات الاختيارية: 6 ساعات معتمدة</li>
            </ul>
          </div>

          <div className="requirement-card">
            <h3 className="requirement-title">متطلبات الكلية</h3>
            <p className="requirement-hours">66 ساعة معتمدة</p>
            <ul className="requirement-list">
              <li>علوم أساسية: 24 ساعة معتمدة إجبارية</li>
              <li>علوم حاسب أساسية: 42 ساعة معتمدة إجبارية</li>
            </ul>
          </div>

          <div className="requirement-card">
            <h3 className="requirement-title">متطلبات التخصص</h3>
            <p className="requirement-hours">60 ساعة معتمدة</p>
            <ul className="requirement-list">
              <li>المقررات الإجبارية: 42 ساعة معتمدة إجبارية</li>
              <li>المقررات الاختيارية: 12 ساعة معتمدة</li>
              <li>مشروع التخرج: 6 ساعات معتمدة</li>
            </ul>
          </div>
        </div>

        <div className="total-requirements">
          <h3 className="total-title">إجمالي الساعات المطلوبة للتخرج</h3>
          <div className="total-hours">138 ساعة معتمدة</div>
          <p className="total-note">
            بالإضافة إلى التدريب العملي والميداني ومقرر حقوق الإنسان ومكافحة
            الفساد (لا يحسب في المعدل التراكمي للطالب)
          </p>
        </div>
      </div>

      {/* Academic Curriculum Section */}
      <div className="content-section">
        <h2 className="section-title">الجداول الدراسية</h2>

        <div className="curriculum-intro">
          <p className="paragraph">
            تشمل الخطة الدراسية للحصول على درجة البكالوريوس في الحاسبات
            والمعلومات المقررات التالية موزعة على أربعة مستويات دراسية (ثمانية
            فصول دراسية):
          </p>
        </div>

        <div className="level-section">
          <h3 className="level-title">المستوى الأول (السنة الأولى)</h3>

          <div className="semester-grid">
            <div className="semester-card">
              <h4 className="semester-title">الفصل الدراسي الأول</h4>
              <div className="courses-table">
                <div className="course-row header">
                  <span>رمز المقرر</span>
                  <span>اسم المقرر</span>
                  <span>الساعات</span>
                </div>
                <div className="course-row">
                  <span>BS101</span>
                  <span>رياضة (1)</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>BS102</span>
                  <span>فيزياء (1)</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS101</span>
                  <span>مقدمة في الحاسبات</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS102</span>
                  <span>برمجة (1)</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>HU100</span>
                  <span>اللغة الإنجليزية</span>
                  <span>2</span>
                </div>
                <div className="course-row">
                  <span>HU102</span>
                  <span>القضايا الاجتماعية والأخلاقية</span>
                  <span>2</span>
                </div>
                <div className="course-row total">
                  <span></span>
                  <span>
                    <strong>إجمالي الساعات</strong>
                  </span>
                  <span>
                    <strong>16</strong>
                  </span>
                </div>
              </div>
            </div>

            <div className="semester-card">
              <h4 className="semester-title">الفصل الدراسي الثاني</h4>
              <div className="courses-table">
                <div className="course-row header">
                  <span>رمز المقرر</span>
                  <span>اسم المقرر</span>
                  <span>الساعات</span>
                </div>
                <div className="course-row">
                  <span>BS103</span>
                  <span>رياضة (2)</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>BS104</span>
                  <span>فيزياء (2)</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS103</span>
                  <span>برمجة (2)</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS104</span>
                  <span>هياكل البيانات</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>HU101</span>
                  <span>كتابة التقارير ومهارات العرض</span>
                  <span>2</span>
                </div>
                <div className="course-row">
                  <span>HU1XX</span>
                  <span>مقرر اختياري</span>
                  <span>2</span>
                </div>
                <div className="course-row total">
                  <span></span>
                  <span>
                    <strong>إجمالي الساعات</strong>
                  </span>
                  <span>
                    <strong>16</strong>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="level-section">
          <h3 className="level-title">المستوى الثاني (السنة الثانية)</h3>

          <div className="semester-grid">
            <div className="semester-card">
              <h4 className="semester-title">الفصل الدراسي الثالث</h4>
              <div className="courses-table">
                <div className="course-row header">
                  <span>رمز المقرر</span>
                  <span>اسم المقرر</span>
                  <span>الساعات</span>
                </div>
                <div className="course-row">
                  <span>BS201</span>
                  <span>رياضة متقطعة</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>BS202</span>
                  <span>إحصاء واحتمالات</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS201</span>
                  <span>البرمجة الكائنية</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS202</span>
                  <span>تحليل وتصميم الخوارزميات</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS203</span>
                  <span>تنظيم الحاسبات</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>HU1XX</span>
                  <span>مقرر اختياري</span>
                  <span>2</span>
                </div>
                <div className="course-row total">
                  <span></span>
                  <span>
                    <strong>إجمالي الساعات</strong>
                  </span>
                  <span>
                    <strong>17</strong>
                  </span>
                </div>
              </div>
            </div>

            <div className="semester-card">
              <h4 className="semester-title">الفصل الدراسي الرابع</h4>
              <div className="courses-table">
                <div className="course-row header">
                  <span>رمز المقرر</span>
                  <span>اسم المقرر</span>
                  <span>الساعات</span>
                </div>
                <div className="course-row">
                  <span>BS203</span>
                  <span>رياضة تطبيقية</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS204</span>
                  <span>قواعد البيانات</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS205</span>
                  <span>نظم التشغيل</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS206</span>
                  <span>شبكات الحاسب</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS207</span>
                  <span>هندسة البرمجيات</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>HU1XX</span>
                  <span>مقرر اختياري</span>
                  <span>2</span>
                </div>
                <div className="course-row total">
                  <span></span>
                  <span>
                    <strong>إجمالي الساعات</strong>
                  </span>
                  <span>
                    <strong>17</strong>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="level-section">
          <h3 className="level-title">
            المستوى الثالث والرابع (السنة الثالثة والرابعة)
          </h3>
          <div className="specialization-note">
            <p className="paragraph">
              <strong>ملاحظة:</strong> يبدأ التخصص من المستوى الثالث، ويختار
              الطالب أحد التخصصات التالية:
            </p>
            <div className="specialization-grid">
              <div className="spec-card">
                <h4>علوم الحاسب</h4>
                <p>Computer Science</p>
              </div>
              <div className="spec-card">
                <h4>نظم المعلومات</h4>
                <p>Information Systems</p>
              </div>
              <div className="spec-card">
                <h4>تكنولوجيا المعلومات</h4>
                <p>Information Technology</p>
              </div>
              <div className="spec-card">
                <h4>الجيوإنفورماتيك</h4>
                <p>Geoinformatics</p>
              </div>
              <div className="spec-card">
                <h4>بحوث العمليات ودعم القرار</h4>
                <p>Operations Research & Decision Support</p>
              </div>
            </div>
          </div>

          <div className="semester-grid">
            <div className="semester-card">
              <h4 className="semester-title">الفصل الدراسي الخامس</h4>
              <div className="courses-table">
                <div className="course-row header">
                  <span>رمز المقرر</span>
                  <span>اسم المقرر</span>
                  <span>الساعات</span>
                </div>
                <div className="course-row">
                  <span>CS301</span>
                  <span>مقرر تخصص إجباري (1)</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS302</span>
                  <span>مقرر تخصص إجباري (2)</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS303</span>
                  <span>مقرر تخصص إجباري (3)</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS3XX</span>
                  <span>مقرر تخصص اختياري (1)</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS3XX</span>
                  <span>مقرر تخصص اختياري (2)</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>HU1XX</span>
                  <span>مقرر اختياري</span>
                  <span>2</span>
                </div>
                <div className="course-row total">
                  <span></span>
                  <span>
                    <strong>إجمالي الساعات</strong>
                  </span>
                  <span>
                    <strong>17</strong>
                  </span>
                </div>
              </div>
            </div>

            <div className="semester-card">
              <h4 className="semester-title">الفصل الدراسي السادس</h4>
              <div className="courses-table">
                <div className="course-row header">
                  <span>رمز المقرر</span>
                  <span>اسم المقرر</span>
                  <span>الساعات</span>
                </div>
                <div className="course-row">
                  <span>CS304</span>
                  <span>مقرر تخصص إجباري (4)</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS305</span>
                  <span>مقرر تخصص إجباري (5)</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS306</span>
                  <span>مقرر تخصص إجباري (6)</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS3XX</span>
                  <span>مقرر تخصص اختياري (3)</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS3XX</span>
                  <span>مقرر تخصص اختياري (4)</span>
                  <span>3</span>
                </div>
                <div className="course-row total">
                  <span></span>
                  <span>
                    <strong>إجمالي الساعات</strong>
                  </span>
                  <span>
                    <strong>15</strong>
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="semester-grid">
            <div className="semester-card">
              <h4 className="semester-title">الفصل الدراسي السابع</h4>
              <div className="courses-table">
                <div className="course-row header">
                  <span>رمز المقرر</span>
                  <span>اسم المقرر</span>
                  <span>الساعات</span>
                </div>
                <div className="course-row">
                  <span>CS401</span>
                  <span>مقرر تخصص إجباري (7)</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS402</span>
                  <span>مقرر تخصص إجباري (8)</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS403</span>
                  <span>مشروع التخرج (1)</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS4XX</span>
                  <span>مقرر تخصص اختياري (5)</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS4XX</span>
                  <span>مقرر تخصص اختياري (6)</span>
                  <span>3</span>
                </div>
                <div className="course-row total">
                  <span></span>
                  <span>
                    <strong>إجمالي الساعات</strong>
                  </span>
                  <span>
                    <strong>15</strong>
                  </span>
                </div>
              </div>
            </div>

            <div className="semester-card">
              <h4 className="semester-title">الفصل الدراسي الثامن</h4>
              <div className="courses-table">
                <div className="course-row header">
                  <span>رمز المقرر</span>
                  <span>اسم المقرر</span>
                  <span>الساعات</span>
                </div>
                <div className="course-row">
                  <span>CS404</span>
                  <span>مقرر تخصص إجباري (9)</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS405</span>
                  <span>مقرر تخصص إجباري (10)</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS406</span>
                  <span>مشروع التخرج (2)</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS4XX</span>
                  <span>مقرر تخصص اختياري (7)</span>
                  <span>3</span>
                </div>
                <div className="course-row">
                  <span>CS4XX</span>
                  <span>مقرر تخصص اختياري (8)</span>
                  <span>3</span>
                </div>
                <div className="course-row total">
                  <span></span>
                  <span>
                    <strong>إجمالي الساعات</strong>
                  </span>
                  <span>
                    <strong>15</strong>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="total-credit-summary">
          <h3 className="summary-title">ملخص إجمالي الساعات المعتمدة</h3>
          <div className="summary-grid">
            <div className="summary-item">
              <span className="summary-label">المستوى الأول:</span>
              <span className="summary-value">32 ساعة</span>
            </div>
            <div className="summary-item">
              <span className="summary-label">المستوى الثاني:</span>
              <span className="summary-value">34 ساعة</span>
            </div>
            <div className="summary-item">
              <span className="summary-label">المستوى الثالث:</span>
              <span className="summary-value">32 ساعة</span>
            </div>
            <div className="summary-item">
              <span className="summary-label">المستوى الرابع:</span>
              <span className="summary-value">30 ساعة</span>
            </div>
            <div className="summary-item total-summary">
              <span className="summary-label">الإجمالي الكلي:</span>
              <span className="summary-value">128 ساعة</span>
            </div>
            <div className="summary-item additional">
              <span className="summary-label">التدريب العملي:</span>
              <span className="summary-value">شهر واحد</span>
            </div>
            <div className="summary-item additional">
              <span className="summary-label">مقرر حقوق الإنسان:</span>
              <span className="summary-value">لا يحسب في المعدل</span>
            </div>
          </div>
        </div>
      </div>

      {/* Training and Project Section */}
      <div className="content-section">
        <h2 className="section-title">التدريب العملي ومشروع التخرج</h2>

        <div className="training-section">
          <h3 className="article-title">التدريب العملي والميداني</h3>
          <p className="paragraph">
            يجب على الطالب حضور التدريب العملي والميداني لمدة شهر قبل التخرج
            خلال أي عطلة صيفية بعد اجتيازه 60 ساعة معتمدة. وذلك تحت إشراف أعضاء
            هيئة التدريس والهيئة المعاونة وذلك لمتابعة المشاركين في التدريب ووضع
            التقييم الخاص بكل منهم طبقاً للمعايير التي يتم تحديدها من قبل مجالس
            الأقسام المختصة ويعتمدها مجلس الكلية.
          </p>
        </div>

        <div className="project-section">
          <h3 className="article-title">مشروع التخرج</h3>
          <p className="paragraph">
            يقوم طالب المستوى الرابع بإعداد مشروع بكالوريوس في موضوعات متعلقة
            بتخصصهم تحددها مجالس الأقسام المختصة وذلك خلال العام الدراسي كله.
            ويجوز تخصيص فترة إضافية للمشروع تبدأ بعد الانتهاء من امتحان الفصل
            الدراسي الثاني ولمدة أربعة أسابيع على الأكثر وتكون تحت إشراف أعضاء
            هيئة التدريس لتنظيم إعداد المشاريع وإخراجها في صورتها النهائية
            لمناقشتها.
          </p>
          <div className="project-details">
            <ul>
              <li>يقدر المشروع بقيمة (6) ساعات معتمدة ويمتد لفصلين دراسيين</li>
              <li>
                يقدم الطالب تقريراً علمياً عن موضوع مشروع التخرج في نهاية الفترة
                المخصصة للمشروع
              </li>
              <li>
                يشكل مجلس الكلية لجنة مناقشة وتقييم التقارير الخاصة بالمشاريع
                المقدمة من الطلاب
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Additional Regulations Section */}
      <div className="content-section">
        <h2 className="section-title">مواد إضافية ولوائح تنظيمية</h2>

        <div className="article-section">
          <h3 className="article-title">مادة (13): الرسوب والإعادة</h3>
          <p className="paragraph">
            أ- إذا رسب الطالب في مقرر دراسي وجب عليه إعادة دراسة هذا المقرر في
            أول فرصة تتاح له.
          </p>
          <p className="paragraph">
            ب- إذا نجح الطالب في المقرر المعاد يحتسب له التقدير الجديد فقط في
            حساب المعدل التراكمي.
          </p>
          <p className="paragraph">
            ج- لا يجوز للطالب إعادة مقرر نجح فيه لتحسين تقديره إلا في حالات
            استثنائية يقررها مجلس الكلية.
          </p>
        </div>

        <div className="article-section">
          <h3 className="article-title">مادة (14): الانتقال بين المستويات</h3>
          <p className="paragraph">
            أ- ينتقل الطالب من مستوى إلى آخر بعد اجتيازه بنجاح العدد المطلوب من
            الساعات المعتمدة:
          </p>
          <div className="level-requirements">
            <div className="level-req-item">
              <span className="level-name">للانتقال إلى المستوى الثاني:</span>
              <span className="level-hours">
                اجتياز 24 ساعة معتمدة على الأقل
              </span>
            </div>
            <div className="level-req-item">
              <span className="level-name">للانتقال إلى المستوى الثالث:</span>
              <span className="level-hours">
                اجتياز 54 ساعة معتمدة على الأقل
              </span>
            </div>
            <div className="level-req-item">
              <span className="level-name">للانتقال إلى المستوى الرابع:</span>
              <span className="level-hours">
                اجتياز 90 ساعة معتمدة على الأقل
              </span>
            </div>
          </div>
        </div>

        <div className="article-section">
          <h3 className="article-title">
            مادة (15): متطلبات الحصول على الدرجة
          </h3>
          <p className="paragraph">
            للحصول على درجة البكالوريوس في الحاسبات والمعلومات يجب على الطالب:
          </p>
          <div className="degree-requirements">
            <ul>
              <li>اجتياز جميع المقررات المطلوبة بنجاح (138 ساعة معتمدة)</li>
              <li>الحصول على معدل تراكمي لا يقل عن 2.0</li>
              <li>إنجاز مشروع التخرج بنجاح</li>
              <li>إتمام فترة التدريب العملي والميداني</li>
              <li>اجتياز مقرر حقوق الإنسان ومكافحة الفساد</li>
              <li>عدم وجود مخالفات أكاديمية أو تأديبية</li>
            </ul>
          </div>
        </div>

        <div className="article-section">
          <h3 className="article-title">مادة (16): التدريب العملي والميداني</h3>
          <p className="paragraph">
            أ- يجب على الطالب إتمام فترة تدريب عملي وميداني لمدة شهر واحد على
            الأقل في إحدى الجهات المعتمدة من الكلية.
          </p>
          <p className="paragraph">
            ب- يتم التدريب تحت إشراف أعضاء هيئة التدريس والهيئة المعاونة
            بالكلية.
          </p>
          <p className="paragraph">
            ج- يقدم الطالب تقريراً مفصلاً عن فترة التدريب ويتم تقييمه من قبل
            المشرف الأكاديمي والجهة المضيفة.
          </p>
        </div>

        <div className="article-section">
          <h3 className="article-title">
            مادة (17): مشروع التخرج والمناقشة والتقرير
          </h3>
          <p className="paragraph">
            أ- يقوم طالب المستوى الرابع بإعداد مشروع تخرج في موضوع متعلق بتخصصه
            تحت إشراف أحد أعضاء هيئة التدريس.
          </p>
          <p className="paragraph">
            ب- يمتد المشروع لفصلين دراسيين (6 ساعات معتمدة) ويقدم الطالب تقريراً
            نهائياً عن المشروع.
          </p>
          <p className="paragraph">
            ج- تشكل لجنة من ثلاثة أعضاء من هيئة التدريس لمناقشة وتقييم المشروع.
          </p>
        </div>

        <div className="article-section">
          <h3 className="article-title">
            مادة (18): الإشراف العلمي على تدريس المقررات
          </h3>
          <p className="paragraph">
            يتولى الإشراف على تدريس المقررات أعضاء هيئة التدريس المتخصصون في كل
            قسم، ويجوز الاستعانة بأعضاء هيئة التدريس من الجامعات الأخرى أو
            الخبراء المتخصصين وفقاً للقواعد المنظمة لذلك.
          </p>
        </div>

        <div className="article-section">
          <h3 className="article-title">
            مادة (19): التعليم عن بُعد والإلكتروني
          </h3>
          <p className="paragraph">
            أ- يجوز للكلية تطبيق نظام التعليم عن بُعد والتعليم الإلكتروني في بعض
            المقررات وفقاً للمعايير والضوابط التي يضعها مجلس الكلية.
          </p>
          <p className="paragraph">
            ب- تحدد نسبة التعليم عن بُعد بما لا يتجاوز 30% من إجمالي المقررات
            الدراسية.
          </p>
        </div>

        <div className="article-section">
          <h3 className="article-title">مادة (20): بيان الدرجات</h3>
          <p className="paragraph">
            يحصل الطالب على بيان درجات يوضح جميع المقررات التي درسها والدرجات
            التي حصل عليها والمعدل الفصلي والتراكمي، ويصدر هذا البيان من إدارة
            شئون الطلاب بالكلية.
          </p>
        </div>

        <div className="article-section">
          <h3 className="article-title">مادة (21): نظام الاستماع</h3>
          <p className="paragraph">
            يجوز للطالب التسجيل كمستمع في بعض المقررات دون احتساب درجات، وذلك
            وفقاً للشروط والضوابط التي يضعها مجلس الكلية، ولا تحتسب هذه المقررات
            ضمن الساعات المطلوبة للتخرج.
          </p>
        </div>

        <div className="article-section">
          <h3 className="article-title">مادة (22): تطبيق اللائحة</h3>
          <p className="paragraph">
            أ- تطبق هذه اللائحة على جميع الطلاب المقيدين بالكلية اعتباراً من
            العام الجامعي 2019/2020.
          </p>
          <p className="paragraph">
            ب- يفسر مجلس الكلية نصوص هذه اللائحة ويضع القواعد التنفيذية اللازمة
            لتطبيقها.
          </p>
          <p className="paragraph">
            ج- في حالة عدم النص على حكم معين في هذه اللائحة، يطبق ما ورد في
            اللائحة العامة للجامعات المصرية.
          </p>
        </div>
      </div>

      {/* Footer Section */}
      <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-2xl p-6 md:p-8 mt-12 text-white shadow-2xl">
        <div className="flex flex-col md:flex-row items-center justify-between gap-6">
          <div className="flex-1 text-center md:text-right">
            <p className="text-base md:text-lg leading-relaxed">
              هذه اللائحة معتمدة من مجلس الكلية ومجلس الجامعة وفقاً للقوانين
              واللوائح المنظمة للتعليم العالي في جمهورية مصر العربية
            </p>
          </div>
          <div className="flex-shrink-0">
            <img
              src={collegeLogo}
              alt="College Logo"
              className="w-20 h-20 md:w-24 md:h-24 rounded-full border-3 border-white/30 object-cover"
            />
          </div>
        </div>
      </div>

      {/* Scroll Buttons */}
      <div className="fixed bottom-6 left-6 flex flex-col gap-3 z-50">
        {/* Scroll to Bottom Button */}
        <button
          onClick={scrollToBottom}
          className="bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 focus:outline-none focus:ring-4 focus:ring-blue-300"
          title="انتقل إلى الأسفل"
        >
          <HiChevronDown className="w-6 h-6" />
        </button>

        {/* Scroll to Top Button */}
        {showScrollButton && (
          <button
            onClick={scrollToTop}
            className="bg-gray-600 hover:bg-gray-700 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 focus:outline-none focus:ring-4 focus:ring-gray-300 animate-fade-in"
            title="العودة إلى الأعلى"
          >
            <ArrowUpIcon className="w-6 h-6" />
          </button>
        )}
      </div>
    </div>
  );
};

export default CollegeGuide;
